import anthropic
from loguru import logger
from .llm_client_base import LLMClientBase
from ...config import config


class AnthropicClient(LLMClientBase):
    def __init__(self, model_id=None):
        self.model_id = model_id or config.llm.anthropic.model_id
        self.api_key = config.llm.anthropic.api_key
        self._initialize_client()

    def _initialize_client(self):
        try:
            self.client = anthropic.Anthropic(api_key=self.api_key)
            logger.info("🤖 Anthropic client initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Anthropic client: {e}")
            raise

    def call_llm(self, prompt: str) -> str:
        try:
            message = self.client.messages.create(
                model=self.model_id,
                max_tokens=30000,
                stream=False,
                messages=[
                    {
                        "role": "user",
                        "content": prompt,
                    }
                ],
                extra_headers={"Content-Type": "application/json"},
            )
            return message.content[0].text
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            raise
