"""
Tool-specific dynamic instructions that are injected into prompts based on the tools being used.
This keeps the main prompt clean while providing detailed guidance when specific tools are needed.
"""

from typing import Dict, List, Optional, Any
from .constants import SEARCH_CONFIG

# Get display values from config
CHUNKING_THRESHOLD = SEARCH_CONFIG["chunking_threshold"]
CHUNK_SIZE = SEARCH_CONFIG["chunk_size"]


# Semantic Search Guidance - HOW to use semantic search effectively
SEMANTIC_SEARCH_GUIDANCE = {
    "basic": f"""
=== HOW TO USE SEMANTIC SEARCH ===
• **For discovery only** - when you don't know what exists in the codebase
• **Use broad terms** - "authentication", "database connection", "API routes"
• **Analyze patterns** - compare multiple implementations to understand architecture
• **Extract keywords** - note specific function/class names for follow-up database queries
• **Don't over-search** - 2-3 semantic searches max, then implement with findings
""",
    "with_chunking": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Large files chunked** into {CHUNK_SIZE}-line segments - use fetch_next_code: true to continue
• **Store actual code patterns** with line numbers when found
• **Look for implementation patterns** that can be reused for current and future tasks
""",
    "sequential_code": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Multiple implementations found** - pick one pattern and implement immediately
• **Don't collect all patterns** - implement with current findings
• **Use fetch_next_code: true** only if completely blocked and need more info
""",
    "sequential_code_last": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Enough patterns found** - implement immediately with current knowledge
• **Stop collecting** - use what you have to make progress
• **Implement now** rather than searching more
""",
    "single_code_node": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Single implementation found** - examine it thoroughly to understand all functions it uses
• **Validate all functions** - check their signatures, parameters, and dependencies
• **Implement immediately** if you have what you need, don't search for more patterns
""",
    "metadata_only": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Metadata found** - use database queries to get actual code
• **Focus on promising files** that might contain implementation patterns
• **Get code snippets** rather than just metadata
""",
    "no_results": f"""
=== SEMANTIC DISCOVERY GUIDANCE ===
• **Nothing found** - functionality doesn't exist in project, create from scratch
• **Look for similar patterns** in codebase to understand how to implement
• **Use ls only if needed** to understand project structure before implementing new features
""",
}

# Database Search Guidance - HOW to use database queries effectively
DATABASE_SEARCH_GUIDANCE = {
    "basic": f"""
=== HOW TO USE DATABASE QUERIES ===
• **For exact retrieval** - when you know specific names/paths
• **Use precise queries** - GET_CODE_FROM_FILE for files, GET_NODES_BY_EXACT_NAME for functions
• **Get complete implementations** - fetch full code, not just metadata
• **Validate dependencies** - use GET_FUNCTION_CALLERS/CALLEES to understand relationships
• **Most efficient tool** - fastest when you know what you're looking for
""",
    "with_chunking": f"""
=== DATABASE RETRIEVAL ===
• **Large files chunked** - use fetch_next_code: true for remaining segments
• **Store relevant code** with line numbers for implementation
""",
    "sequential_code": f"""
=== DATABASE RETRIEVAL ===
• **Multiple entities** - use fetch_next_code: true for next item
• **Store implementation details** for all relevant entities
""",
    "sequential_code_last": f"""
=== DATABASE RETRIEVAL ===
• **All entities retrieved** - proceed with implementation using stored code
""",
    "single_code_node": f"""
=== DATABASE RETRIEVAL ===
• **Single entity found** - analyze and store implementation details
""",
    "metadata_only": f"""
=== DATABASE RETRIEVAL ===
• **Metadata only** - use GET_CODE_FROM_FILE to get actual code
""",
    "no_results": f"""
=== DATABASE RETRIEVAL ===
• **Nothing found** - check spelling or try semantic search
""",
    "line_specific": f"""
=== DATABASE RETRIEVAL ===
• **Line range specified** - focus on exact code sections needed
""",
}

# Centralized Thinking Process - How to Choose Tools Efficiently
CENTRALIZED_THINKING_PROCESS = """
=== MANDATORY TOOL SELECTION PROCESS ===

**CRITICAL: FOLLOW THIS EXACT DECISION TREE EVERY TIME:**

**FORBIDDEN ACTIONS:**
   • NEVER use `ls` command - use `rg --files` instead for directory listing
   • NEVER use semantic search when you have exact function/file names
   • NEVER guess with rg - only use known keywords
   • NEVER use database queries for discovery

**REQUIRED DECISION PROCESS:**

**I HAVE EXACT NAMES/PATHS** → USE DATABASE ONLY
   Examples:
   • "getUserData function" → GET_NODES_BY_EXACT_NAME with name: "getUserData"
   • "src/auth/login.js file" → GET_CODE_FROM_FILE with file_path: "src/auth/login.js"
   • "UserController class" → GET_NODES_BY_EXACT_NAME with name: "UserController"

**I HAVE KEYWORDS OR NEED TERMINAL OPERATIONS** → USE TERMINAL
   Examples:
   • Found "createUser" in semantic search → rg "createUser"
   • Found "express.Router" in database → rg "express.Router"
   • Need files in directory → rg --files "path/to/directory"
   • Test code/run commands → python test.py, npm test, cargo build
   • File operations → cat, grep, find, mkdir, cp, mv

**I KNOW NOTHING ABOUT WHAT EXISTS** → USE SEMANTIC SEARCH ONLY
   Examples:
   • "How does authentication work?" → semantic search
   • "Find API patterns" → semantic search
   • "Understand project structure" → semantic search

**IMPLEMENTATION RULE:**
   After 2-3 searches, IMPLEMENT with what you have. Don't keep searching.

**VIOLATION CONSEQUENCES:**
   Using wrong tool = inefficient search = wasted time = poor user experience
"""

# Tool-specific instruction templates - Simplified
TOOL_INSTRUCTIONS = {
    "semantic_search": SEMANTIC_SEARCH_GUIDANCE,
    "database": DATABASE_SEARCH_GUIDANCE,
    "git_diff": {
        "basic": """
=== GIT DIFF FORMAT REQUIREMENTS ===
CRITICAL: Every git diff MUST follow this EXACT format or it will fail:

1. **FILE HEADERS** (MANDATORY):
   --- a/relative/path/to/file.ext
   +++ b/relative/path/to/file.ext

2. **HUNK HEADER** (MANDATORY):
   @@ -old_start,old_count +new_start,new_count @@

3. **CONTENT RULES**:
   • Context lines: no prefix (unchanged lines)
   • Deletions: - prefix (lines to remove)
   • Additions: + prefix (lines to add)
   • Include 3 context lines before and after changes

4. **COMPLETE EXAMPLE**:
--- a/src/router.js
+++ b/src/router.js
@@ -5,7 +5,8 @@
 const express = require('express');
 const router = express.Router();

-router.get('/old-endpoint', oldHandler);
+router.get('/new-endpoint', newHandler);
+router.post('/additional-endpoint', additionalHandler);

 module.exports = router;

**CRITICAL**: Missing headers or incorrect format will cause "patch fragment without header" errors.
""",
    },
    "terminal": {
        "basic": """
=== HOW TO USE TERMINAL COMMANDS ===
• **Search operations**: `rg -A 5 -B 5 "exact_keyword"` with known terms only
• **Directory exploration**: `rg --files "path"` (NEVER use ls command)
• **File operations**: `cat file.py`, `grep "pattern" file.txt`, `find . -name "*.js"`
• **Testing/Running**: `python test.py`, `npm test`, `cargo build`, `make install`
• **Development tasks**: `mkdir`, `cp`, `mv`, `chmod`, `git status`
• **Pattern matching**: `rg "import.*express"` for specific patterns
• **Never guess keywords** - only use search terms you've already discovered
""",
        "search_failure": """
=== TERMINAL COMMAND FAILED ===
• **Search failed**: Keyword doesn't exist or command error occurred
• **Switch tools**: Use semantic search for discovery or database for exact names
• **Command errors**: Check syntax, file permissions, or try alternative commands
• **Don't keep guessing**: Try different approach after 2-3 failed attempts
""",
    },
}


def get_centralized_thinking_process() -> str:
    """
    Get the centralized thinking process for tool selection.

    Returns:
        Centralized thinking process instruction string
    """
    return CENTRALIZED_THINKING_PROCESS


def get_tool_specific_instructions(
    tool_type: str,
    context: Optional[Dict[str, Any]] = None
) -> str:
    """
    Get tool-specific instructions based on the tool type and context.

    Args:
        tool_type: The type of tool being used
        context: Additional context that might affect instruction selection

    Returns:
        Tool-specific instruction string
    """
    if tool_type not in TOOL_INSTRUCTIONS:
        return ""

    tool_instructions = TOOL_INSTRUCTIONS[tool_type]

    # Determine which instruction variant to use based on context
    if context:
        # Check for terminal search failure context (highest priority for terminal)
        if tool_type == "terminal" and context.get("search_failure", False):
            return tool_instructions.get(
                "search_failure", tool_instructions.get("basic", "")
            )

        # Check for no results context (highest priority for semantic search)
        if context.get("no_results", False):
            return tool_instructions.get("no_results", tool_instructions.get("basic", ""))

        # Check for chunking context
        if context.get("has_chunking", False):
            return tool_instructions.get("with_chunking", tool_instructions.get("basic", ""))

        # Check for single code node context
        if context.get("single_code_node", False):
            return tool_instructions.get("single_code_node", tool_instructions.get("basic", ""))

        # Check for sequential code delivery context
        if context.get("sequential_code", False):
            # Choose guidance based on whether there are more nodes
            if context.get("has_more_nodes", False):
                return tool_instructions.get("sequential_code", tool_instructions.get("basic", ""))
            else:
                return tool_instructions.get("sequential_code_last", tool_instructions.get("basic", ""))

        # Check for metadata-only context
        if context.get("metadata_only", False):
            return tool_instructions.get("metadata_only", tool_instructions.get("basic", ""))

        # Check for line-specific context
        if context.get("line_specific", False):
            return tool_instructions.get("line_specific", tool_instructions.get("basic", ""))

    # Default to basic instructions
    return tool_instructions.get("basic", "")


def build_dynamic_tool_instructions(
    tools_used: List[str],
    context: Optional[Dict[str, Any]] = None
) -> str:
    """
    Build dynamic tool instructions for multiple tools.
    
    Args:
        tools_used: List of tool types that will be or have been used
        context: Context information for instruction customization
        
    Returns:
        Combined tool-specific instructions
    """
    instructions = []
    
    for tool_type in tools_used:
        tool_instruction = get_tool_specific_instructions(tool_type, context)
        if tool_instruction:
            instructions.append(tool_instruction)
    
    return "\n".join(instructions)


def determine_tool_context(
    last_action: Optional[Dict[str, Any]] = None,
    last_action_result: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Determine context for tool instructions based on previous actions and results.

    Args:
        last_action: The last action that was executed
        last_action_result: The result of the last action

    Returns:
        Context dictionary for tool instruction selection
    """
    context = {}

    if not last_action_result:
        return context

    # Check for terminal search failures
    if last_action_result.get("tool_type") == "terminal":
        command = last_action_result.get("command", "")
        success = last_action_result.get("success", True)
        output = last_action_result.get("output", "")

        # Detect search commands that failed or returned no results
        search_commands = ["rg", "grep", "find", "ls"]
        is_search_command = any(cmd in command for cmd in search_commands)

        if is_search_command and (
            not success or not output.strip() or "No such file" in output
        ):
            context["search_failure"] = True

    # Check for chunking indicators
    if "chunk" in str(last_action_result).lower():
        context["has_chunking"] = True

    # Check for metadata-only results
    if last_action and last_action.get("parameters", {}).get("code_snippet") is False:
        context["metadata_only"] = True

    # Check for zero results and sequential delivery
    if last_action_result.get("tool_type") == "semantic_search":
        total_nodes = last_action_result.get("total_nodes", 0)
        code_snippet = last_action_result.get("code_snippet", False)
        current_node = last_action_result.get("current_node", 1)
        has_more_nodes = last_action_result.get("has_more_nodes", False)

        if total_nodes == 0:
            context["no_results"] = True
        elif code_snippet and total_nodes == 1:
            # Single node with code content
            context["single_code_node"] = True
        elif code_snippet and total_nodes > 1:
            # Sequential code delivery - multiple nodes with code content
            context["sequential_code"] = True
            # Only show fetch_next_code guidance if there are actually more nodes
            if has_more_nodes or current_node < total_nodes:
                context["has_more_nodes"] = True

    # Check for line-specific queries
    if last_action and (
        "start_line" in last_action.get("parameters", {})
        or "end_line" in last_action.get("parameters", {})
    ):
        context["line_specific"] = True

    # Check for chunking indicators
    result_data = last_action_result.get("data", {})
    if isinstance(result_data, dict):
        # Look for explicit chunk indicators
        if "chunk_info" in result_data or "total_chunks" in result_data:
            context["has_chunking"] = True
        elif any("chunk" in str(key).lower() for key in result_data.keys()):
            context["has_chunking"] = True
        else:
            # Look for line count indicators that suggest chunking (>250 lines)
            for value in result_data.values():
                if "lines" in str(value).lower():
                    # Extract numbers from the value to check if any indicate large files
                    import re
                    numbers = re.findall(r'\d+', str(value))
                    for num_str in numbers:
                        if int(num_str) > CHUNKING_THRESHOLD:  # Use config threshold
                            context["has_chunking"] = True
                            break
                    if context.get("has_chunking"):
                        break

    return context


def build_terminal_search_fallback_content(has_search_failure: bool = False) -> str:
    """
    Build terminal search fallback content for database tools section.

    Args:
        has_search_failure: Whether terminal search commands failed

    Returns:
        Fallback content string or empty string
    """
    if not has_search_failure:
        return ""

    return """
  - GET_NODES_BY_KEYWORD_SEARCH: {{"query_name": "GET_NODES_BY_KEYWORD_SEARCH", "keyword": "search_keyword", "code_snippet": true|false}} - Global search in code content and node names (case-sensitive for code)"""
